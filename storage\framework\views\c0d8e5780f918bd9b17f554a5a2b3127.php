<?php $__env->startSection('title', 'Daftar - Sistem Kursus'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-vh-100 d-flex align-items-center py-5" style="background: #F8FAFC;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-7 col-xl-6">
                <div class="card border-0" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                    <!-- Header -->
                    <div class="card-header text-center py-4" style="background: #578FCA; border: none; border-radius: 16px 16px 0 0;">
                        <div class="mb-3">
                            <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-white" style="width: 60px; height: 60px;">
                                <i class="fas fa-user-plus fa-lg" style="color: #3674B5;"></i>
                            </div>
                        </div>
                        <h4 class="text-white fw-600 mb-2" style="font-family: 'Poppins', sans-serif; font-size: 1.25rem;">Bergabung Dengan Kami</h4>
                        <p class="text-white mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem; opacity: 0.8;">Buat akun baru untuk memulai pembelajaran</p>
                    </div>

                    <div class="card-body p-4">
                        <?php if($errors->any()): ?>
                            <div class="alert border-0 mb-4" style="background: #F5F0CD; color: #7F1D1D; border-radius: 12px;">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-exclamation-circle me-2 mt-1" style="color: #3674B5;"></i>
                                    <div style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="mb-1"><?php echo e($error); ?></div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <form action="<?php echo e(route('register')); ?>" method="POST">
                            <?php echo csrf_field(); ?>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label fw-500 mb-2" style="color: #578FCA; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                        <i class="fas fa-user me-2"></i>Nama Lengkap
                                    </label>
                                    <input type="text"
                                           class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="name"
                                           name="name"
                                           value="<?php echo e(old('name')); ?>"
                                           placeholder="Masukkan nama lengkap"
                                           style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 0.75rem 1rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"
                                           required>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label fw-500 mb-2" style="color: #578FCA; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                        <i class="fas fa-phone me-2"></i>Nomor Telepon
                                    </label>
                                    <input type="tel"
                                           class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="phone"
                                           name="phone"
                                           value="<?php echo e(old('phone')); ?>"
                                           placeholder="08xxxxxxxxxx"
                                           style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 0.75rem 1rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"
                                           required>
                                    <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label fw-500 mb-2" style="color: #578FCA; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                    <i class="fas fa-envelope me-2"></i>Email Address
                                </label>
                                <input type="email"
                                       class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="email"
                                       name="email"
                                       value="<?php echo e(old('email')); ?>"
                                       placeholder="<EMAIL>"
                                       style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 0.75rem 1rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"
                                       required>
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label fw-500 mb-2" style="color: #578FCA; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                        <i class="fas fa-lock me-2"></i>Password
                                    </label>
                                    <div class="position-relative">
                                        <input type="password"
                                               class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="password"
                                               name="password"
                                               placeholder="Minimal 8 karakter"
                                               style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 0.75rem 1rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"
                                               required>
                                        <button type="button" class="btn position-absolute end-0 top-50 translate-middle-y me-2"
                                                onclick="togglePassword('password', 'toggleIcon1')" style="border: none; background: none;">
                                            <i class="fas fa-eye" id="toggleIcon1" style="color: #578FCA;"></i>
                                        </button>
                                    </div>
                                    
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="password_confirmation" class="form-label fw-500 mb-2" style="color: #578FCA; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                        <i class="fas fa-lock me-2"></i>Konfirmasi Password
                                    </label>
                                    <div class="position-relative">
                                        <input type="password"
                                               class="form-control"
                                               id="password_confirmation"
                                               name="password_confirmation"
                                               placeholder="Ulangi password"
                                               style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 0.75rem 1rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"
                                               required>
                                        <button type="button" class="btn position-absolute end-0 top-50 translate-middle-y me-2"
                                                onclick="togglePassword('password_confirmation', 'toggleIcon2')" style="border: none; background: none;">
                                            <i class="fas fa-eye absolute" id="toggleIcon2" style="color: #578FCA;"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="form-check d-flex align-items-start">
                                    <input type="checkbox" class="form-check-input mt-1" id="terms" required
                                           style="border-radius: 6px;">
                                    <label class="form-check-label ms-2 text-muted" for="terms" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                        Saya setuju dengan
                                        <a href="#" class="text-decoration-none fw-500" style="color: #3674B5;">
                                            Syarat dan Ketentuan
                                        </a>
                                        serta
                                        <a href="#" class="text-decoration-none fw-500" style="color: #3674B5;">
                                            Kebijakan Privasi
                                        </a>
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid mb-4">
                                <button type="submit" class="btn fw-500"
                                        style="background: #3674B5; border: none; border-radius: 12px; padding: 0.75rem 1rem; color: white; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                    <i class="fas fa-user-plus me-2"></i>Daftar Sekarang
                                </button>
                            </div>
                        </form>

                        <div class="text-center">
                            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                Sudah punya akun?
                                <a href="<?php echo e(route('login')); ?>" class="text-decoration-none fw-500"
                                   style="color: #3674B5;">
                                    Masuk di sini
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(inputId, iconId) {
    const passwordInput = document.getElementById(inputId);
    const toggleIcon = document.getElementById(iconId);

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Add focus effects
document.querySelectorAll('.form-control').forEach(input => {
    input.addEventListener('focus', function() {
        this.style.borderColor = '#3674B5';
        this.style.boxShadow = '0 0 0 0.2rem rgba(255, 63, 51, 0.25)';
    });

    input.addEventListener('blur', function() {
        this.style.borderColor = '#E5E7EB';
        this.style.boxShadow = 'none';
    });
});

// Button hover effect
document.querySelector('button[type="submit"]').addEventListener('mouseenter', function() {
    this.style.background = '#578FCA';
});

document.querySelector('button[type="submit"]').addEventListener('mouseleave', function() {
    this.style.background = '#3674B5';
});

</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views/auth/register.blade.php ENDPATH**/ ?>