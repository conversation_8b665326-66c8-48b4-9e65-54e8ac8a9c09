@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON> - Admin')

@section('content')
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="mb-2">
                <i class="fas fa-credit-card me-2 text-primary"></i>Kelola Pembayaran
            </h1>
            <p class="text-muted mb-0">Verifikasi dan kelola pembayaran kursus</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-warning text-white">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4>{{ $payments->where('status', 'pending')->count() }}</h4>
                    <small>Menunggu Verifikasi</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-success text-white">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4>{{ $payments->where('status', 'verified')->count() }}</h4>
                    <small>Terverifikasi</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-danger text-white">
                <div class="card-body">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                    <h4>{{ $payments->where('status', 'rejected')->count() }}</h4>
                    <small>Ditolak</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-info text-white">
                <div class="card-body">
                    <i class="fas fa-money-bill fa-2x mb-2"></i>
                    <h4>{{ number_format($payments->where('status', 'verified')->sum('amount') / 1000000, 1) }}M</h4>
                    <small>Total Pendapatan</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.payments.index') }}">
                <div class="row align-items-end">
                    <div class="col-md-4 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">Semua Status</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="verified" {{ request('status') == 'verified' ? 'selected' : '' }}>Verified</option>
                            <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="search" class="form-label">Cari</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Nama siswa atau kursus...">
                    </div>
                    <div class="col-md-4 mb-3">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('admin.payments.index') }}" class="btn fw-500"
                           style="background: #F5F0CD; color: #3674B5; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: 2px solid #3674B5;">
                            <i class="fas fa-undo me-1"></i>Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>Daftar Pembayaran
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Siswa</th>
                            <th>Kursus</th>
                            <th>Jumlah</th>
                            <th>Metode</th>
                            <th>Status</th>
                            <th>Tanggal</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($payments as $payment)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary rounded-circle p-2 me-2">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ $payment->user->name }}</h6>
                                            <small class="text-muted">{{ $payment->user->email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <h6 class="mb-0">{{ $payment->course->title }}</h6>
                                    <small class="text-muted">{{ $payment->course->formatted_price }}</small>
                                </td>
                                <td>
                                    <strong class="text-primary">
                                        Rp {{ number_format($payment->amount, 0, ',', '.') }}
                                    </strong>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">
                                        {{ $payment->payment_method }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $payment->status == 'verified' ? 'success' : ($payment->status == 'pending' ? 'warning' : 'danger') }}">
                                        @if($payment->status == 'verified')
                                            <i class="fas fa-check me-1"></i>Verified
                                        @elseif($payment->status == 'pending')
                                            <i class="fas fa-clock me-1"></i>Pending
                                        @else
                                            <i class="fas fa-times me-1"></i>Rejected
                                        @endif
                                    </span>
                                </td>
                                <td>
                                    <div>{{ $payment->created_at->format('d M Y') }}</div>
                                    <small class="text-muted">{{ $payment->created_at->format('H:i') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        @if($payment->proof_file)
                                            <a href="{{ route('admin.payments.download', $payment) }}" 
                                               class="btn btn-outline-primary btn-sm" title="Download Bukti">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        @endif
                                        
                                        @if($payment->status == 'pending')
                                            <button class="btn btn-outline-success btn-sm" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#verifyModal{{ $payment->id }}"
                                                    title="Verifikasi">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#rejectModal{{ $payment->id }}"
                                                    title="Tolak">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Tidak Ada Pembayaran</h5>
                                    <p class="text-muted mb-0">Belum ada pembayaran yang perlu diverifikasi</p>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    @if($payments->hasPages())
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    {{ $payments->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Payment Verification Modals -->
@foreach($payments as $payment)
    @if($payment->status == 'pending')
        <!-- Verify Modal -->
        <div class="modal fade" id="verifyModal{{ $payment->id }}" tabindex="-1" aria-labelledby="verifyModalLabel{{ $payment->id }}" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content" style="border-radius: 16px; border: none;">
                    <div class="modal-header border-0" style="padding: 1.5rem 1.5rem 0;">
                        <h5 class="modal-title fw-600" id="verifyModalLabel{{ $payment->id }}" style="color: #3674B5; font-family: 'Poppins', sans-serif;">
                            <i class="fas fa-check-circle me-2" style="color: #578FCA;"></i>
                            Verifikasi Pembayaran
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form action="{{ route('admin.payments.verify', $payment) }}" method="POST">
                        @csrf
                        @method('PATCH')
                        <input type="hidden" name="status" value="verified">
                        <div class="modal-body" style="padding: 0 1.5rem;">
                            <div class="alert" style="background: #F5F0CD; border: 1px solid #3674B5; border-radius: 12px; color: #3674B5;">
                                <h6 style="color: #3674B5; font-family: 'Poppins', sans-serif; font-weight: 600;">Detail Pembayaran:</h6>
                                <ul class="mb-0" style="font-family: 'Poppins', sans-serif;">
                                    <li>Siswa: {{ $payment->user->name }}</li>
                                    <li>Kursus: {{ $payment->course->title }}</li>
                                    <li>Jumlah: Rp {{ number_format($payment->amount, 0, ',', '.') }}</li>
                                    <li>Metode: {{ $payment->payment_method }}</li>
                                </ul>
                            </div>
                            <div class="mb-3">
                                <label for="notes{{ $payment->id }}" class="form-label" style="color: #3674B5; font-family: 'Poppins', sans-serif; font-weight: 500;">Catatan (Opsional)</label>
                                <textarea class="form-control" id="notes{{ $payment->id }}" name="notes" rows="3"
                                          placeholder="Tambahkan catatan verifikasi..."
                                          style="border-radius: 8px; border: 1px solid #ddd; font-family: 'Poppins', sans-serif;"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer border-0" style="padding: 0 1.5rem 1.5rem;">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                    style="border-radius: 8px; font-family: 'Poppins', sans-serif;">Batal</button>
                            <button type="submit" class="btn"
                                    style="background: #578FCA; border: none; color: white; border-radius: 8px; font-family: 'Poppins', sans-serif;">
                                <i class="fas fa-check me-1"></i>Verifikasi Pembayaran
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Reject Modal -->
        <div class="modal fade" id="rejectModal{{ $payment->id }}" tabindex="-1" aria-labelledby="rejectModalLabel{{ $payment->id }}" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content" style="border-radius: 16px; border: none;">
                    <div class="modal-header border-0" style="padding: 1.5rem 1.5rem 0;">
                        <h5 class="modal-title fw-600" id="rejectModalLabel{{ $payment->id }}" style="color: #3674B5; font-family: 'Poppins', sans-serif;">
                            <i class="fas fa-times-circle me-2" style="color: #3674B5;"></i>
                            Tolak Pembayaran
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form action="{{ route('admin.payments.verify', $payment) }}" method="POST">
                        @csrf
                        @method('PATCH')
                        <input type="hidden" name="status" value="rejected">
                        <div class="modal-body" style="padding: 0 1.5rem;">
                            <div class="alert" style="background: #F5F0CD; border: 1px solid #3674B5; border-radius: 12px; color: #3674B5;">
                                <h6 style="color: #3674B5; font-family: 'Poppins', sans-serif; font-weight: 600;">Detail Pembayaran:</h6>
                                <ul class="mb-0" style="font-family: 'Poppins', sans-serif;">
                                    <li>Siswa: {{ $payment->user->name }}</li>
                                    <li>Kursus: {{ $payment->course->title }}</li>
                                    <li>Jumlah: Rp {{ number_format($payment->amount, 0, ',', '.') }}</li>
                                    <li>Metode: {{ $payment->payment_method }}</li>
                                </ul>
                            </div>
                            <div class="mb-3">
                                <label for="reject_notes{{ $payment->id }}" class="form-label" style="color: #3674B5; font-family: 'Poppins', sans-serif; font-weight: 500;">
                                    Alasan Penolakan <span style="color: #3674B5;">*</span>
                                </label>
                                <textarea class="form-control" id="reject_notes{{ $payment->id }}" name="notes" rows="3"
                                          placeholder="Jelaskan alasan penolakan pembayaran..." required
                                          style="border-radius: 8px; border: 1px solid #ddd; font-family: 'Poppins', sans-serif;"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer border-0" style="padding: 0 1.5rem 1.5rem;">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                    style="border-radius: 8px; font-family: 'Poppins', sans-serif;">Batal</button>
                            <button type="submit" class="btn"
                                    style="background: #3674B5; border: none; color: white; border-radius: 8px; font-family: 'Poppins', sans-serif;">
                                <i class="fas fa-times me-1"></i>Tolak Pembayaran
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
@endforeach

@endsection
