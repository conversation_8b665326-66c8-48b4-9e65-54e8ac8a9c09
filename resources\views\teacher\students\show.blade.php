@extends('layouts.app')

@section('title', 'Detail Siswa - Sistem Kursus')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1" style="color: #3674B5; font-family: 'Poppins', sans-serif; font-weight: 600;">
                        <i class="fas fa-user me-2"></i>Detail Siswa
                    </h2>
                    <p class="text-muted mb-0">{{ $student->name }}</p>
                </div>
                <a href="{{ route('teacher.students.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Kembali
                </a>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <!-- Student Info -->
        <div class="col-md-4 mb-4">
            <div class="card" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div class="card-header text-center" style="background: linear-gradient(135deg, #578FCA 0%, #9FC87E 100%); border-radius: 15px 15px 0 0; border: none;">
                    <div class="py-3">
                        <div class="rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                             style="width: 80px; height: 80px; background: rgba(255,255,255,0.2); color: white; font-size: 2rem; font-weight: bold;">
                            {{ strtoupper(substr($student->name, 0, 2)) }}
                        </div>
                        <h5 class="text-white mb-1" style="font-family: 'Poppins', sans-serif;">{{ $student->name }}</h5>
                        <small class="text-white opacity-75">{{ $student->email }}</small>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="small fw-semibold text-muted">EMAIL</label>
                        <p class="mb-0">{{ $student->email }}</p>
                    </div>
                    @if($student->phone)
                        <div class="mb-3">
                            <label class="small fw-semibold text-muted">TELEPON</label>
                            <p class="mb-0">{{ $student->phone }}</p>
                        </div>
                    @endif
                    <div class="mb-3">
                        <label class="small fw-semibold text-muted">BERGABUNG</label>
                        <p class="mb-0">{{ $student->created_at->format('d M Y') }}</p>
                    </div>
                    <div class="mb-0">
                        <label class="small fw-semibold text-muted">TOTAL KURSUS</label>
                        <p class="mb-0">{{ $enrollments->count() }} kursus</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enrollments -->
        <div class="col-md-8 mb-4">
            <div class="card" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div class="card-header" style="background: #578FCA; border-radius: 15px 15px 0 0; border: none;">
                    <h5 class="mb-0 text-white" style="font-family: 'Poppins', sans-serif; font-weight: 600;">
                        <i class="fas fa-book me-2"></i>Kursus yang Diikuti
                    </h5>
                </div>
                <div class="card-body p-0">
                    @forelse($enrollments as $enrollment)
                        <div class="d-flex align-items-center p-4 border-bottom">
                            <div class="me-3">
                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 40px; height: 40px; background: #F5F0CD; color: #3674B5;">
                                    <i class="fas fa-book"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0" style="color: #578FCA;">{{ $enrollment->course->title }}</h6>
                                    <span class="badge {{ $enrollment->status == 'active' ? 'bg-success' : ($enrollment->status == 'completed' ? 'bg-primary' : 'bg-secondary') }}">
                                        {{ ucfirst($enrollment->status) }}
                                    </span>
                                </div>
                                <div class="d-flex align-items-center text-muted small">
                                    <i class="fas fa-calendar me-1"></i>
                                    Daftar: {{ $enrollment->enrolled_at->format('d M Y') }}
                                    <span class="mx-2">•</span>
                                    <i class="fas fa-money-bill me-1"></i>
                                    {{ $enrollment->course->formatted_price }}
                                </div>
                            </div>
                            <div class="ms-3">
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" 
                                            data-bs-toggle="dropdown">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        @if($enrollment->status != 'active')
                                            <li>
                                                <form action="{{ route('teacher.enrollments.update-status', $enrollment) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <input type="hidden" name="status" value="active">
                                                    <button type="submit" class="dropdown-item">
                                                        <i class="fas fa-check-circle text-success me-2"></i>Aktifkan
                                                    </button>
                                                </form>
                                            </li>
                                        @endif
                                        @if($enrollment->status != 'completed')
                                            <li>
                                                <form action="{{ route('teacher.enrollments.update-status', $enrollment) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <input type="hidden" name="status" value="completed">
                                                    <button type="submit" class="dropdown-item">
                                                        <i class="fas fa-graduation-cap text-primary me-2"></i>Tandai Selesai
                                                    </button>
                                                </form>
                                            </li>
                                        @endif
                                        @if($enrollment->status != 'cancelled')
                                            <li>
                                                <form action="{{ route('teacher.enrollments.update-status', $enrollment) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <input type="hidden" name="status" value="cancelled">
                                                    <button type="submit" class="dropdown-item">
                                                        <i class="fas fa-times-circle text-danger me-2"></i>Batalkan
                                                    </button>
                                                </form>
                                            </li>
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="fas fa-book-open fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Siswa belum mengikuti kursus Anda</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Reviews -->
    @if($reviews->count() > 0)
        <div class="row">
            <div class="col-12">
                <div class="card" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <div class="card-header" style="background: linear-gradient(135deg, #9FC87E 0%, #e8f5e8 100%); border-radius: 15px 15px 0 0; border: none;">
                        <h5 class="mb-0 text-white" style="font-family: 'Poppins', sans-serif; font-weight: 600;">
                            <i class="fas fa-star me-2"></i>Review dari Siswa
                        </h5>
                    </div>
                    <div class="card-body">
                        @foreach($reviews as $review)
                            <div class="mb-3 p-3 rounded-3" style="background: #f8f9fa;">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0" style="color: #578FCA;">{{ $review->course->title }}</h6>
                                    <div class="text-warning">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star{{ $i <= $review->rating ? '' : '-o' }}"></i>
                                        @endfor
                                    </div>
                                </div>
                                <p class="mb-1">{{ $review->comment }}</p>
                                <small class="text-muted">{{ $review->created_at->format('d M Y') }}</small>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.alert {
    border-radius: 12px;
    border: none;
}
</style>
@endsection
